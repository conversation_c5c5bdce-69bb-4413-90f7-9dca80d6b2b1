name: Build and Release

on:
    release:
        types: [created]

jobs:
    build:
        name: Build and Upload Release Assets
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                os: [ubuntu-latest, windows-latest, macos-latest]
                include:
                    - os: ubuntu-latest
                      artifact_name: kiro2cc
                      asset_prefix: kiro2cc-linux-amd64
                    - os: windows-latest
                      artifact_name: kiro2cc.exe
                      asset_prefix: kiro2cc-windows-amd64
                    - os: macos-latest
                      artifact_name: kiro2cc
                      asset_prefix: kiro2cc-macos-amd64

        steps:
            - name: Checkout code
              uses: actions/checkout@v3

            - name: Set up Go
              uses: actions/setup-go@v4
              with:
                  go-version: '1.23.3'
                  check-latest: true

            - name: Build
              run: go build -v -o ${{ matrix.artifact_name }} .

            - name: Install UPX
              shell: bash
              run: |
                  if [ "${{ runner.os }}" == "Linux" ]; then
                    sudo apt-get update && sudo apt-get install -y upx
                  elif [ "${{ runner.os }}" == "macOS" ]; then
                    brew install upx
                  elif [ "${{ runner.os }}" == "Windows" ]; then
                    choco install upx -y
                  fi

            - name: Compress binary with UPX
              shell: bash
              run: |
                  if [ "${{ runner.os }}" != "macOS" ]; then
                    upx --best --lzma ${{ matrix.artifact_name }}
                  else
                    echo "Skipping UPX compression for macOS as it's not supported"
                  fi

            - name: Extract version from tag
              id: get_version
              shell: bash
              run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

            - name: Upload Release Asset
              uses: actions/upload-release-asset@v1
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              with:
                  upload_url: ${{ github.event.release.upload_url }}
                  asset_path: ./${{ matrix.artifact_name }}
                  asset_name: ${{ matrix.asset_prefix }}-${{ steps.get_version.outputs.VERSION }}${{ endsWith(matrix.artifact_name, '.exe') && '.exe' || '' }}
                  asset_content_type: application/octet-stream
